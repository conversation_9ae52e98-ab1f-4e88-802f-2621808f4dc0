const AppService = require('./AppService');
const UserJobVacanciesRepository = require('../repositories/UserJobVacanciesRepository');
const { JobVacancy, User, UserProfile } = require('../models');

class UserJobVacanciesService extends AppService {
  constructor() {
    super();
    this.repository = new UserJobVacanciesRepository();
  }

  /**
   * Get all user job vacancy recommendations with pagination and filtering
   * @param {Object} params - Query params (page, limit, job_vacancy_id, etc.)
   * @returns {Object} - UserJobVacancy records array and pagination info
   */
  async findAll(params = {}) {
    // Validate job_vacancy_id exists if provided
    if (params.job_vacancy_id) {
      const jobVacancy = await JobVacancy.findByPk(params.job_vacancy_id);
      this.exists(jobVacancy, 'Job vacancy not found');
    }

    params['sort'] = params.sort || 'match_rate';
    params['sort_direction'] = params.sort_direction || 'desc';

    const { rows, pagination } = await this.repository.findAll(params);
    const users = await User.findAll({
      where: { id: rows.map(row => row.user_id) },
      include: [
        {
          model: UserProfile,
          as: 'profile',
          attributes: ['current_position'],
        },
      ],
    });

    const userJobVacancies = rows.map(row => ({
      ...row.dataValues,
      user: users.find(user => user.id === row.user_id),
    }));
    console.log('userJobVacancies', userJobVacancies);

    return {
      userJobVacancies,
      pagination,
      // Pass additional context that might be useful for the output
      jobVacancyId: params.job_vacancy_id || null,
    };
  }

  /**
   * Find a specific user job vacancy recommendation by ID
   * @param {number} id - UserJobVacancy ID
   * @returns {Object} - UserJobVacancy with user and profile data
   */
  async findById(id) {
    const userJobVacancy = await this.repository.model.findByPk(id, {
      include: [
        {
          model: this.repository.model.sequelize.models.User,
          as: 'user',
          attributes: ['id', 'name'],
          include: [
            {
              model: this.repository.model.sequelize.models.UserProfile,
              as: 'profile',
              attributes: ['current_position'],
            },
          ],
        },
      ],
    });

    this.exists(userJobVacancy, 'User job vacancy recommendation not found');
    return userJobVacancy;
  }
}

module.exports = UserJobVacanciesService;
